<nb-card class="request-item-import-dialog">
  <nb-card-header class="request-item-import-header">
    <div class="request-item-import-title">需求項目匯入</div>
    <button class="close-btn" nbButton ghost (click)="close()">
      <nb-icon icon="close-outline"></nb-icon>
    </button>
  </nb-card-header>

  <nb-card-body class="request-item-import-body">
    <!-- 步驟導航 -->
    <div class="step-nav">
      <div class="step-item" [ngClass]="{
        'active': currentStep === 1,
        'completed': currentStep > 1,
        'pending': currentStep < 1
      }">1. 選擇項目</div>
      <div class="step-item" [ngClass]="{
        'active': currentStep === 2,
        'completed': currentStep > 2,
        'pending': currentStep < 2
      }">2. 確認匯入</div>
    </div>
    <!-- 步驟1: 選擇需求項目 -->
    <div *ngIf="currentStep === 1" class="step-content">
      <!-- 搜尋區塊 -->
      <div class="search-section mb-4">
        <div class="section-title">
          <nb-icon icon="search-outline" class="mr-2"></nb-icon>搜尋條件
        </div>
        <div class="search-form">
          <div class="row">
            <div class="form-group col-12 col-md-6">
              <label for="location" class="label mb-2">區域</label>
              <input type="text" nbInput id="location" placeholder="請輸入區域" [(ngModel)]="searchRequest.CLocation">
            </div>
            <div class="form-group col-12 col-md-6">
              <label for="requirement" class="label mb-2">工程項目</label>
              <input type="text" nbInput id="requirement" placeholder="請輸入工程項目"
                [(ngModel)]="searchRequest.CRequirement">
            </div>
          </div>
          <div class="row">
            <div class="col-12 text-right">
              <button class="btn btn-secondary me-2" (click)="resetSearch()">
                <nb-icon icon="refresh-outline"></nb-icon>
                重置
              </button>
              <button class="btn btn-primary" (click)="onSearch()">
                <nb-icon icon="search-outline"></nb-icon>
                搜尋
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 需求項目選擇區域 -->
      <div class="requirement-selection">
        <div class="section-title">
          <nb-icon icon="layers-outline" class="mr-2"></nb-icon>選擇需求項目
        </div>

        <!-- 載入中 -->
        <div *ngIf="loading" class="text-center py-4">
          <div class="spinner-border text-primary" role="status">
            <span class="sr-only">載入中...</span>
          </div>
          <div class="mt-2">載入需求項目中...</div>
        </div>

        <!-- 需求項目列表 -->
        <div *ngIf="!loading" class="requirement-list">
          <div *ngIf="requirements.length > 0; else noRequirements">
            <div *ngFor="let requirement of requirements" class="requirement-item">
              <nb-checkbox [(ngModel)]="requirement.selected" (ngModelChange)="onRequirementItemChange()">
                <div class="requirement-info">
                  <div class="item-name">{{ requirement.CRequirement || '未命名需求' }}</div>
                  <div class="item-code">位置: {{ requirement.CLocation || '未指定' }}</div>
                  <div class="item-status">
                    單位: {{ requirement.CUnit || '未指定' }}
                  </div>
                  <div class="item-type">
                    單價: {{ requirement.CUnitPrice | currency:'TWD':'symbol-narrow':'1.0-0' }}
                  </div>
                  <div *ngIf="requirement.CRemark" class="item-remark">
                    備註: {{ requirement.CRemark }}
                  </div>
                </div>
              </nb-checkbox>
            </div>
          </div>
          <ng-template #noRequirements>
            <div class="no-requirements">
              <nb-icon icon="info-outline" class="mr-2"></nb-icon>
              沒有可匯入的需求項目，請調整搜尋條件或聯繫管理員
            </div>
          </ng-template>
        </div>
      </div>
    </div>

    <!-- 步驟2: 確認匯入 -->
    <div *ngIf="currentStep === 2" class="step-content">
      <div class="confirmation-area">
        <div class="section-title">
          <nb-icon icon="checkmark-circle-outline" class="mr-2"></nb-icon>確認匯入詳情
        </div>

        <div class="selected-summary">
          <div class="summary-text">
            將匯入 <strong>{{ getSelectedCount() }}</strong> 個需求項目到目前的建案中
          </div>
        </div>

        <div class="selected-requirements-details">
          <div *ngFor="let item of getSelectedItems(); let i = index" class="requirement-detail-section">
            <div class="requirement-detail-header">
              <h6 class="requirement-name">{{ i + 1 }}. {{ item.CRequirement || '未命名需求' }}</h6>
              <div class="requirement-meta">
                <div class="requirement-location">位置: {{ item.CLocation || '未指定' }}</div>
                <div class="requirement-unit">單位: {{ item.CUnit || '未指定' }}</div>
                <div class="requirement-price">單價: {{ item.CUnitPrice | currency:'TWD':'symbol-narrow':'1.0-0' }}</div>
              </div>
              <div *ngIf="item.CRemark" class="requirement-remark">
                備註: {{ item.CRemark }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </nb-card-body>

  <nb-card-footer class="request-item-import-footer">
    <div class="step-buttons">
      <button *ngIf="currentStep > 1" nbButton ghost (click)="previousStep()" class="mr-2">
        <nb-icon icon="arrow-back-outline" class="mr-1"></nb-icon>
        上一步
      </button>

      <button nbButton ghost (click)="close()" class="mr-2">
        取消
      </button>

      <button *ngIf="currentStep < 2" nbButton status="primary" [disabled]="!canProceed()" (click)="nextStep()">
        下一步
        <nb-icon icon="arrow-forward-outline" class="mr-1"></nb-icon>
      </button>

      <button *ngIf="currentStep === 2" nbButton status="success" (click)="importRequirements()">
        <nb-icon icon="download-outline" class="mr-1"></nb-icon>
        確認匯入
      </button>
    </div>
  </nb-card-footer>
</nb-card>