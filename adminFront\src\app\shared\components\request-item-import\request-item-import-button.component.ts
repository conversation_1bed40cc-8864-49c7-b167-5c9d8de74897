import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NbButtonModule, NbIconModule, NbComponentSize, NbComponentStatus } from '@nebular/theme';
import { RequestItemImportService, RequestItemImportServiceConfig } from './request-item-import.service';
import { RequestItemImportConfig } from './request-item-import.component';

@Component({
  selector: 'app-request-item-import-button',
  standalone: true,
  imports: [
    CommonModule,
    NbButtonModule,
    NbIconModule
  ],
  template: `
    <button 
      nbButton
      [status]="status"
      [size]="size"
      [disabled]="disabled || !buildCaseId"
      [class]="buttonClass"
      (click)="openImportDialog()">
      <nb-icon *ngIf="icon" [icon]="icon" class="me-1"></nb-icon>
      {{ text }}
    </button>
  `
})
export class RequestItemImportButtonComponent {
  @Input() buildCaseId: number = 0;
  @Input() text: string = '需求項目匯入';
  @Input() icon: string = 'download-outline';
  @Input() buttonClass: string = '';
  @Input() disabled: boolean = false;
  @Input() status: NbComponentStatus = 'primary';
  @Input() size: NbComponentSize = 'medium';
  @Input() config?: RequestItemImportServiceConfig;

  @Output() itemsImported = new EventEmitter<RequestItemImportConfig>();
  @Output() beforeOpen = new EventEmitter<void>();
  @Output() error = new EventEmitter<string>();

  constructor(private importService: RequestItemImportService) {}

  openImportDialog() {
    this.beforeOpen.emit();

    const serviceConfig: RequestItemImportServiceConfig = {
      buildCaseId: this.buildCaseId,
      buttonText: this.text,
      buttonIcon: this.icon,
      buttonClass: this.buttonClass,
      ...this.config
    };

    this.importService.openImportDialog(serviceConfig)
      .subscribe({
        next: (result) => {
          if (result) {
            this.itemsImported.emit(result);
          }
        },
        error: (error) => {
          this.error.emit('開啟匯入對話框時發生錯誤');
        }
      });
  }
}