import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {
  NbCardModule,
  NbButtonModule,
  NbIconModule,
  NbCheckboxModule,
  NbSelectModule,
  NbOptionModule,
  NbInputModule,
  NbDialogRef
} from '@nebular/theme';
import { RequirementService } from 'src/services/api/services/requirement.service';
import { BuildCaseService } from 'src/services/api/services';
import { GetListRequirementRequest, GetRequirement, GetRequirementListResponseBase, BuildCaseGetListReponse } from 'src/services/api/models';
import { EnumHelper } from 'src/app/shared/helper/enumHelper';
import { EnumHouseType } from 'src/app/shared/enum/enumHouseType';

export interface ExtendedRequirementItem extends GetRequirement {
  selected?: boolean;
}

export interface RequestItemImportConfig {
  buildCaseId: number;
  buildCaseName?: string;
  selectedItems: ExtendedRequirementItem[];
  totalItems: number;
  searchCriteria?: {
    CHouseType?: number[];
    CLocation?: string;
    CRequirement?: string;
  };
}

@Component({
  selector: 'app-request-item-import',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NbCardModule,
    NbButtonModule,
    NbIconModule,
    NbCheckboxModule,
    NbSelectModule,
    NbOptionModule,
    NbInputModule
  ],
  templateUrl: './request-item-import.component.html',
  styleUrls: ['./request-item-import.component.scss']
})
export class RequestItemImportComponent implements OnInit {
  @Input() buildCaseId: number = 0;
  @Output() itemsImported = new EventEmitter<RequestItemImportConfig>();

  currentStep: number = 1;
  requirements: ExtendedRequirementItem[] = [];
  loading: boolean = false;

  // 搜尋相關屬性
  searchRequest: GetListRequirementRequest & { CIsShow?: boolean | null; CIsSimple?: boolean | null } = {};
  buildCaseList: BuildCaseGetListReponse[] = [];
  houseTypeOptions: any[] = [];

  constructor(
    private requirementService: RequirementService,
    private dialogRef: NbDialogRef<RequestItemImportComponent>,
    private enumHelper: EnumHelper,
    private buildCaseService: BuildCaseService
  ) { }

  ngOnInit() {
    this.initializeSearchForm();
    this.getBuildCaseList();
  }

  // 初始化搜尋表單
  initializeSearchForm() {
    this.houseTypeOptions = this.enumHelper.getEnumOptions(EnumHouseType);
    this.searchRequest.CStatus = 1; // 預設只顯示啟用的項目
    this.searchRequest.CIsShow = null; // 預設只顯示需要顯示的項目
    this.searchRequest.CIsSimple = null;
    this.searchRequest.CRequirement = '';
    this.searchRequest.CLocation = '';
    // 預設全選所有房屋類型
    this.searchRequest.CHouseType = this.houseTypeOptions.map(type => type.value);
  }

  // 取得建案列表
  getBuildCaseList() {
    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} })
      .subscribe({
        next: (res) => {
          if (res.StatusCode === 0 && res.Entries) {
            this.buildCaseList = res.Entries;
            // 如果有傳入建案ID，使用傳入的ID；否則使用第一個建案
            if (this.buildCaseId && this.buildCaseId > 0) {
              this.searchRequest.CBuildCaseID = this.buildCaseId;
            } else if (this.buildCaseList.length > 0) {
              this.searchRequest.CBuildCaseID = this.buildCaseList[0].cID;
            }
            // 載入需求項目
            if (this.searchRequest.CBuildCaseID) {
              this.loadRequirementsFromAPI();
            }
          }
        },
        error: (error) => {
          console.error('載入建案列表失敗:', error);
        }
      });
  }

  // 建案切換事件
  onBuildCaseChange() {
    this.loadRequirementsFromAPI();
  }

  // 搜尋事件
  onSearch() {
    this.loadRequirementsFromAPI();
  }

  // 重置搜尋
  resetSearch() {
    this.initializeSearchForm();
    if (this.buildCaseList && this.buildCaseList.length > 0) {
      this.searchRequest.CBuildCaseID = this.buildCaseList[0].cID;
    }
    this.loadRequirementsFromAPI();
  }

  loadRequirementsFromAPI() {
    if (!this.searchRequest.CBuildCaseID) {
      return;
    }

    this.loading = true;

    // 準備API請求參數
    const getRequirementListArgs: GetListRequirementRequest = {
      CBuildCaseID: this.searchRequest.CBuildCaseID,
      CHouseType: this.searchRequest.CHouseType,
      CLocation: this.searchRequest.CLocation || null,
      CRequirement: this.searchRequest.CRequirement || null,
      CStatus: this.searchRequest.CStatus,
      CIsShow: this.searchRequest.CIsShow,
      CIsSimple: this.searchRequest.CIsSimple,
      PageIndex: 1,
      PageSize: 100
    };

    this.requirementService.apiRequirementGetRequestListForTemplatePost$Json({
      body: getRequirementListArgs
    }).subscribe({
      next: (response: GetRequirementListResponseBase) => {
        this.loading = false;
        if (response.StatusCode === 0 && response.Entries) {
          this.requirements = response.Entries.map(item => ({
            ...item,
            selected: false
          }));
        } else {
          this.requirements = [];
        }
      },
      error: (error) => {
        this.loading = false;
        this.requirements = [];
      }
    });
  }

  onRequirementItemChange() {
    // 當需求項目選擇變更時的處理
  }

  getSelectedItems(): ExtendedRequirementItem[] {
    return this.requirements.filter(item => item.selected);
  }

  canProceed(): boolean {
    switch (this.currentStep) {
      case 1:
        return this.getSelectedItems().length > 0;
      case 2:
        return true;
      default:
        return false;
    }
  }

  nextStep() {
    if (this.canProceed() && this.currentStep < 2) {
      this.currentStep++;
    }
  }

  previousStep() {
    if (this.currentStep > 1) {
      this.currentStep--;
    }
  }

  getProgressText(): string {
    const progressTexts = {
      1: '請選擇要匯入的需求項目',
      2: '確認匯入詳情'
    };
    return progressTexts[this.currentStep as keyof typeof progressTexts] || '';
  }

  importRequirements() {
    const selectedBuildCase = this.buildCaseList.find(bc => bc.cID === this.searchRequest.CBuildCaseID);
    const config: RequestItemImportConfig = {
      buildCaseId: this.searchRequest.CBuildCaseID || 0,
      buildCaseName: selectedBuildCase?.CBuildCaseName || '',
      selectedItems: this.getSelectedItems(),
      totalItems: this.getSelectedItems().length,
      searchCriteria: {
        CHouseType: this.searchRequest.CHouseType || undefined,
        CLocation: this.searchRequest.CLocation || undefined,
        CRequirement: this.searchRequest.CRequirement || undefined
      }
    };

    this.itemsImported.emit(config);
    this.close();
  }

  close() {
    this.resetSelections();
    this.dialogRef.close();
  }

  private resetSelections() {
    this.currentStep = 1;
    this.requirements.forEach(requirement => {
      requirement.selected = false;
    });
  }

  selectAll() {
    const allSelected = this.requirements.every(item => item.selected);
    this.requirements.forEach(item => {
      item.selected = !allSelected;
    });
  }

  getSelectedCount(): number {
    return this.getSelectedItems().length;
  }

  getTotalCount(): number {
    return this.requirements.length;
  }
}
