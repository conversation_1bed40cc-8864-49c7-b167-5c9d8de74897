// Bootstrap badge styles
.badge {
  display: inline-block;
  padding: 0.25em 0.4em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;

  &.badge-primary {
    color: #fff;
    background-color: #007bff;
  }

  &.badge-success {
    color: #fff;
    background-color: #28a745;
  }

  &.badge-info {
    color: #fff;
    background-color: #17a2b8;
  }

  &.badge-secondary {
    color: #fff;
    background-color: #6c757d;
  }
}

/* 需求項目匯入對話框 - 參考 SpaceTemplateSelectorComponent 樣式 */
.request-item-import-dialog {
  min-width: 600px;
  max-width: 800px;
  min-height: 500px;
  max-height: 80vh;

  .request-item-import-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid var(--nb-color-basic-300);
    background-color: var(--nb-color-basic-100);

    .request-item-import-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--nb-color-text-basic);
    }

    .close-btn {
      padding: 0.25rem;
      min-width: auto;
      border: none;
      background: transparent;
      color: var(--nb-color-text-hint);
      transition: all 0.3s ease;

      &:hover {
        color: var(--nb-color-text-basic);
        background-color: var(--nb-color-basic-200);
        border-radius: 0.25rem;
      }

      nb-icon {
        font-size: 1.25rem;
      }
    }
  }

  .request-item-import-body {
    padding: 1.5rem;
    overflow-y: auto;
    max-height: 60vh;
    background-color: var(--nb-color-basic-100);
  }
}

/* 步驟內容 */
.step-content {
  min-height: 300px;

  .section-title {
    display: flex;
    align-items: center;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--nb-color-text-basic);
    margin-bottom: 1rem;

    nb-icon {
      margin-right: 0.5rem;
      color: var(--nb-color-primary-500);
    }
  }

  /* 搜尋區塊 */
  .search-section {
    margin-bottom: 2rem;

    .search-form {
      background-color: var(--nb-color-basic-200);
      padding: 1.5rem;
      border-radius: 0.375rem;
      border: 1px solid var(--nb-color-basic-300);

      .form-group {
        margin-bottom: 1rem;

        .label {
          font-weight: 500;
          color: var(--nb-color-text-basic);
          display: block;
        }

        nb-select,
        input {
          width: 100%;
        }
      }

      .btn {
        min-width: 80px;

        nb-icon {
          margin-right: 0.5rem;
        }
      }
    }
  }

  /* 需求項目選擇區域 */
  .requirement-selection {
    .requirement-list {
      .requirement-item {
        margin-bottom: 1rem;
        padding: 1rem;
        border: 1px solid var(--nb-color-basic-300);
        border-radius: 0.375rem;
        transition: all 0.3s ease;
        background-color: var(--nb-color-basic-100);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        &:hover {
          border-color: var(--nb-color-primary-300);
          box-shadow: 0 4px 8px rgba(0, 123, 255, 0.15);
          background-color: var(--nb-color-basic-200);
        }

        nb-checkbox {
          width: 100%;

          .requirement-info {
            margin-left: 0.5rem;
            flex: 1;

            .item-name {
              font-weight: 600;
              color: var(--nb-color-text-basic);
              margin-bottom: 0.25rem;
            }

            .item-code {
              font-size: 0.875rem;
              color: var(--nb-color-text-hint);
              margin-bottom: 0.25rem;
            }

            .item-status {
              font-size: 0.875rem;
              color: var(--nb-color-text-hint);
              margin-bottom: 0.25rem;
            }

            .item-type {
              font-size: 0.875rem;
              color: var(--nb-color-text-hint);
            }

            .item-remark {
              font-size: 0.875rem;
              color: var(--nb-color-info-600);
              font-style: italic;
              margin-top: 0.25rem;
            }
          }
        }
      }

      .no-requirements {
        text-align: center;
        padding: 2rem;
        color: var(--nb-color-text-hint);
        background-color: var(--nb-color-basic-200);
        border-radius: 0.375rem;
        border: 1px solid var(--nb-color-basic-300);

        nb-icon {
          margin-right: 0.5rem;
          color: var(--nb-color-info-500);
        }
      }
    }
  }

  /* 確認套用區域 */
  .confirmation-area {
    .selected-summary {
      background: linear-gradient(135deg, var(--nb-color-primary-100) 0%, var(--nb-color-primary-50) 100%);
      border: 1px solid var(--nb-color-primary-300);
      border-radius: 0.375rem;
      padding: 1rem;
      margin-bottom: 1.5rem;

      .summary-text {
        color: var(--nb-color-text-basic);
        font-weight: 500;

        strong {
          color: var(--nb-color-primary-600);
        }
      }
    }

    .selected-requirements-details {
      .requirement-detail-section {
        border: 1px solid var(--nb-color-basic-300);
        border-radius: 0.375rem;
        margin-bottom: 1rem;
        overflow: hidden;
        background-color: var(--nb-color-basic-100);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        .requirement-detail-header {
          background: linear-gradient(135deg, var(--nb-color-primary-100) 0%, var(--nb-color-primary-50) 100%);
          padding: 1rem;
          border-bottom: 1px solid var(--nb-color-basic-300);

          .requirement-name {
            margin: 0 0 0.5rem 0;
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--nb-color-text-basic);
          }

          .requirement-meta {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;

            .requirement-location,
            .requirement-unit,
            .requirement-price {
              font-size: 0.875rem;
              color: var(--nb-color-text-hint);
            }
          }

          .requirement-remark {
            font-size: 0.875rem;
            color: var(--nb-color-info-600);
            font-style: italic;
            margin-top: 0.5rem;
          }
        }
      }
    }
  }
}

/* 步驟導航 */
.step-nav {
  display: flex !important;
  justify-content: center !important;
  margin-bottom: 2rem !important;
  border-bottom: 1px solid #e4e9f2 !important;
  padding-bottom: 1rem !important;
  background: transparent !important;

  .step-item {
    padding: 0.75rem 1.5rem !important;
    margin: 0 0.5rem !important;
    border-radius: 0.375rem !important;
    font-weight: 500 !important;
    font-size: 0.9rem !important;
    transition: all 0.3s ease !important;
    cursor: default !important;
    display: inline-block !important;
    text-align: center !important;
    min-width: 120px !important;

    &.active {
      background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
      color: white !important;
      box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3) !important;
      border: none !important;
    }

    &.completed {
      background-color: #28a745 !important;
      color: white !important;
      box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2) !important;
      border: none !important;
    }

    &.pending {
      background-color: #f8f9fa !important;
      color: #6c757d !important;
      border: 1px solid #dee2e6 !important;
    }
  }
}

/* 底部按鈕區域 */
.request-item-import-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--nb-color-basic-300);
  background-color: var(--nb-color-basic-100);

  .step-buttons {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
}

/* 響應式設計 */
@media (max-width: 768px) {
  .request-item-import-dialog {
    min-width: 95vw;
    max-width: 95vw;
    margin: 0.5rem;

    .request-item-import-body {
      padding: 1rem;

      .step-nav {
        .step-item {
          font-size: 0.875rem;
          padding: 0.4rem 0.8rem;
          margin: 0 0.25rem;
        }
      }

      .step-content {
        .search-section {
          .search-form {
            padding: 1rem;

            .form-group {
              margin-bottom: 0.75rem;
            }
          }
        }

        .requirement-selection {
          .requirement-list {
            .requirement-item {
              padding: 0.75rem;

              nb-checkbox {
                .requirement-info {
                  .item-name {
                    font-size: 0.9rem;
                  }

                  .item-code,
                  .item-status,
                  .item-type,
                  .item-remark {
                    font-size: 0.8rem;
                  }
                }
              }
            }
          }
        }
      }
    }

    .request-item-import-footer {
      flex-direction: column;
      gap: 0.75rem;
      align-items: stretch;

      .step-buttons {
        justify-content: center;
      }
    }
  }
}

/* 深色主題支援 */
:host-context(.nb-theme-dark) {
  .request-item-import-dialog {
    .request-item-import-header {
      background-color: var(--nb-color-basic-800);
      border-bottom-color: var(--nb-color-basic-600);

      .request-item-import-title {
        color: var(--nb-color-text-basic);
      }

      .close-btn {
        color: var(--nb-color-text-hint);

        &:hover {
          color: var(--nb-color-text-basic);
          background-color: rgba(255, 255, 255, 0.1);
        }
      }
    }

    .request-item-import-body {
      background-color: var(--nb-color-basic-800);

      .step-nav {
        border-bottom-color: #495057 !important;

        .step-item {
          &.active {
            background: linear-gradient(135deg, #0d6efd 0%, #0a58ca 100%) !important;
            color: white !important;
          }

          &.completed {
            background-color: #198754 !important;
            color: white !important;
          }

          &.pending {
            background-color: #343a40 !important;
            color: #adb5bd !important;
            border-color: #495057 !important;
          }
        }
      }

      .step-content {
        .section-title {
          color: var(--nb-color-text-basic);
        }

        .search-section {
          .search-form {
            background-color: var(--nb-color-basic-700);
            border-color: var(--nb-color-basic-600);
          }
        }

        .requirement-selection {
          .requirement-list {
            .requirement-item {
              background-color: var(--nb-color-basic-700);
              border-color: var(--nb-color-basic-600);

              &:hover {
                box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
              }

              .requirement-info {
                .item-name {
                  color: var(--nb-color-text-basic);
                }

                .item-code,
                .item-status,
                .item-type {
                  color: var(--nb-color-text-hint);
                }
              }
            }

            .no-requirements {
              background-color: var(--nb-color-basic-700);
              border-color: var(--nb-color-basic-600);
              color: var(--nb-color-text-hint);
            }
          }
        }

        .confirmation-area {
          .selected-summary {
            background: linear-gradient(135deg, var(--nb-color-primary-800) 0%, var(--nb-color-primary-900) 100%);
            border-color: var(--nb-color-primary-600);
          }

          .selected-requirements-details {
            .requirement-detail-section {
              background-color: var(--nb-color-basic-700);
              border-color: var(--nb-color-basic-600);

              .requirement-detail-header {
                background: linear-gradient(135deg, var(--nb-color-primary-800) 0%, var(--nb-color-primary-900) 100%);
                border-bottom-color: var(--nb-color-basic-600);
              }
            }
          }
        }
      }
    }

    .request-item-import-footer {
      background-color: var(--nb-color-basic-800);
      border-top-color: var(--nb-color-basic-600);
    }
  }
}

/* nb-dialog 內容區域調整 */
:host {
  display: block;
  width: 100%;
}

/* 確保 nb-checkbox 正常顯示 */
nb-checkbox {
  display: flex;
  align-items: flex-start;
  width: 100%;

  .customised-control-input {
    margin-right: 0.75rem;
    margin-top: 0.125rem;
  }
}

/* 工具類別 */
.mr-1 {
  margin-right: 0.25rem;
}

.mr-2 {
  margin-right: 0.5rem;
}